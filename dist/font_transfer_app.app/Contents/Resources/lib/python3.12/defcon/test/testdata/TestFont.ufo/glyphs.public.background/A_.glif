<?xml version="1.0" encoding="UTF-8"?>
<glyph name="A" format="2">
  <advance height="400" width="300"/>
  <unicode hex="0041"/>
  <note>
    Blah blah blah
  </note>
  <image fileName="A sketch.png" xScale="0.5" yScale="0.5"/>
  <guideline y="720" name="top overshoot" identifier="guide1"/>
  <outline>
    <contour identifier="K4mPW5sVi8">
      <point x="-37" y="-83" identifier="LnWkDg6QIk"/>
      <point x="-79" y="43" identifier="yjY5X1Y7r9"/>
      <point x="-51" y="11" identifier="FNr0MGApIi"/>
      <point x="4" y="89" identifier="i80qybbbqp"/>
      <point x="92" y="86" identifier="6blLRSMWhh"/>
      <point x="-67" y="-38" identifier="zGH5DeCRN6"/>
      <point x="-48" y="-8" identifier="gNcxHmKPQo"/>
      <point x="-63" y="-85" identifier="9FSt2qVWvU"/>
      <point x="66" y="84" identifier="17QdKukVQl"/>
      <point x="83" y="1" identifier="QpumZnw5FF"/>
    </contour>
    <contour identifier="ZCjwZkUd8J">
      <point x="-38" y="-62" identifier="RtIKw1QI4M"/>
      <point x="92" y="-85" identifier="B0Rvq6XEl8"/>
      <point x="1" y="77" identifier="RBGCPdvaEu"/>
      <point x="64" y="69" identifier="tEQzeVNQmk"/>
      <point x="-89" y="15" identifier="G2ytKeNTlk"/>
      <point x="81" y="-64" identifier="BlInvXawYE"/>
      <point x="-44" y="50" identifier="KCGRY7a9Xf"/>
      <point x="-25" y="61" identifier="YB8k6cn6w1"/>
      <point x="42" y="86" identifier="NVUAcP4ZKt"/>
      <point x="23" y="-53" identifier="V46KiFKryI"/>
    </contour>
    <contour identifier="ShsmCDP9cM">
      <point x="88" y="82" identifier="xfK9DVjNdp"/>
      <point x="-49" y="-16" identifier="vcDqXWP99E"/>
      <point x="90" y="-99" identifier="xuGQx7Zien"/>
      <point x="-68" y="-69" identifier="PnDsaoJAvK"/>
      <point x="-84" y="48" identifier="WaA1BVwTWR"/>
      <point x="-3" y="52" identifier="AMja3NAY0k"/>
      <point x="39" y="53" identifier="93HyHTzEv4"/>
      <point x="59" y="-37" identifier="E9I03Ab7Oy"/>
      <point x="98" y="19" identifier="t3ukqXETdS"/>
      <point x="76" y="91" identifier="boQUrWPeZ3"/>
    </contour>
  </outline>
  <lib>
    <dict>
      <key>com.typesupply.defcon.test</key>
      <string>hello</string>
    </dict>
  </lib>
</glyph>
