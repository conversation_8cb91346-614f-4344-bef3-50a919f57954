#!/usr/bin/env python3
"""
Font Transfer App - Standalone Version
Transfers glyph styles from Latin fonts to Malayalam fonts
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import threading
import json
import os
import sys

# Add src directory to path so we can import algorithm
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from algorithm import run_algorithm

# Use user's Application Support directory for config
CONFIG_DIR = os.path.expanduser("~/Library/Application Support/FontTransfer")
CONFIG_FILE = os.path.join(CONFIG_DIR, "config.json")

def load_config():
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, "r") as f:
                return json.load(f)
    except Exception as e:
        print(f"Warning: Could not load config: {e}")
    return {}

def save_config(noto_path, xyz_path):
    try:
        os.makedirs(CONFIG_DIR, exist_ok=True)
        data = {"noto_font": noto_path, "xyz_font": xyz_path}
        with open(CONFIG_FILE, "w") as f:
            json.dump(data, f)
    except Exception as e:
        print(f"Warning: Could not save config: {e}")

def start_process():
    noto_path = noto_entry.get()
    xyz_path = xyz_entry.get()
    if not (noto_path and xyz_path):
        messagebox.showerror("Error", "Select both fonts!")
        return
    output_path = filedialog.asksaveasfilename(
        defaultextension=".ttf", filetypes=[("TTF Font", "*.ttf")]
    )
    if not output_path:
        return

    status_label.config(text="Running...")

    def background_task():
        try:
            result = run_algorithm(noto_path, xyz_path, output_path)
            save_config(noto_path, xyz_path)  # Save last used fonts
            root.after(0, lambda: status_label.config(text=result))
            root.after(0, lambda: messagebox.showinfo("Success", result))
        except Exception as e:
            error_msg = f"Error: {str(e)}"
            print(error_msg)  # Print to console for debugging
            root.after(0, lambda: status_label.config(text="Error occurred"))
            root.after(0, lambda: messagebox.showerror("Error", error_msg))

    threading.Thread(target=background_task).start()

# GUI Setup
root = tk.Tk()
root.title("Font Transfer App")
root.geometry("600x300")

# Add some padding and better layout
main_frame = tk.Frame(root, padx=20, pady=20)
main_frame.pack(fill=tk.BOTH, expand=True)

config = load_config()

# Malayalam Base Font
tk.Label(main_frame, text="Malayalam Base Font:", font=("Arial", 12, "bold")).pack(anchor="w", pady=(0, 5))
noto_entry = tk.Entry(main_frame, width=70, font=("Arial", 10))
noto_entry.pack(fill=tk.X, pady=(0, 5))
if "noto_font" in config:
    noto_entry.insert(0, config["noto_font"])

tk.Button(
    main_frame,
    text="Browse Malayalam Font",
    command=lambda: (noto_entry.delete(0, tk.END), noto_entry.insert(
        0, filedialog.askopenfilename(
            title="Select Malayalam Font",
            filetypes=[("TTF Font", "*.ttf"), ("All Files", "*.*")]
        )
    )),
    font=("Arial", 10)
).pack(pady=(0, 15))

# Donor Latin Font
tk.Label(main_frame, text="Donor Latin Font:", font=("Arial", 12, "bold")).pack(anchor="w", pady=(0, 5))
xyz_entry = tk.Entry(main_frame, width=70, font=("Arial", 10))
xyz_entry.pack(fill=tk.X, pady=(0, 5))
if "xyz_font" in config:
    xyz_entry.insert(0, config["xyz_font"])

tk.Button(
    main_frame,
    text="Browse Latin Font",
    command=lambda: (xyz_entry.delete(0, tk.END), xyz_entry.insert(
        0, filedialog.askopenfilename(
            title="Select Latin Font",
            filetypes=[("TTF Font", "*.ttf"), ("TTC Font", "*.ttc"), ("All Files", "*.*")]
        )
    )),
    font=("Arial", 10)
).pack(pady=(0, 20))

# Run button
run_button = tk.Button(
    main_frame, 
    text="Run Font Transfer", 
    command=start_process,
    font=("Arial", 12, "bold"),
    bg="#4CAF50",
    fg="white",
    padx=20,
    pady=10
)
run_button.pack(pady=(0, 10))

# Status label
status_label = tk.Label(main_frame, text="Ready", font=("Arial", 10), fg="blue")
status_label.pack()

# Info text
info_text = tk.Label(
    main_frame,
    text="This app transfers glyph styles from Latin fonts to Malayalam fonts.\n"
         "Mappings: റ→n, ഠ→o, ധ→w, ന→m, ട→s\n"
         "Placeholders added for: ര, വ, സ",
    font=("Arial", 9),
    fg="gray",
    justify=tk.LEFT
)
info_text.pack(pady=(10, 0))

if __name__ == "__main__":
    print("Font Transfer App Starting...")
    print("Console logs will appear here during processing.")
    root.mainloop()
