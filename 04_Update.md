let’s patch `algorithm.py` so that:

* **Any font can be chosen** in both windows:

  * 1st (Base font) = Malayalam font (Noto or any other).
  * 2nd (Donor font) = Latin font (XYZ or any other).
* The code will not hardcode "Noto". It will work with whatever fonts you select.
* If a glyph is missing in either font, it will log a warning instead of crashing.

---

## 🔧 Patched `src/algorithm.py`

```python
import logging
from fontTools.ttLib import TTFont, newTable

logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")

# Malayalam → Latin mapping
# Keys = Malayalam Unicode codepoints, Values = Latin Unicode codepoints
MAP_TABLE = {
    0x0D31: 0x006E,  # റ = n
    0x0D20: 0x006F,  # ഠ = o
    0x0D27: 0x0077,  # ധ = w
    0x0D28: 0x006D,  # ന = m
    0x0D1F: 0x0073,  # ട = s
}

# Extra Malayalam glyphs to generate placeholders
PLACEHOLDER_GLYPHS = {
    0x0D30: "ര",  # ra
    0x0D35: "വ",  # va
    0x0D38: "സ",  # sa
}

def copy_glyph(src_font, dst_font, src_cp, dst_cp):
    """Copy glyph outline from src_cp (Unicode) in src_font to dst_cp in dst_font."""
    src_cmap = src_font.getBestCmap()
    dst_cmap = dst_font.getBestCmap()

    src_glyph_name = src_cmap.get(src_cp)
    dst_glyph_name = dst_cmap.get(dst_cp)

    if not src_glyph_name:
        logging.warning(f"Missing source glyph U+{src_cp:04X} in donor font")
        return
    if not dst_glyph_name:
        logging.warning(f"Missing destination glyph U+{dst_cp:04X} in base font")
        return

    dst_font['glyf'][dst_glyph_name] = src_font['glyf'][src_glyph_name].deepCopy()
    logging.info(f"Replaced U+{dst_cp:04X} with style from U+{src_cp:04X}")

def add_placeholder(dst_font, codepoint, label):
    """Insert a simple placeholder glyph for a Malayalam codepoint."""
    glyph_name = dst_font.getBestCmap().get(codepoint)
    if not glyph_name:
        logging.warning(f"Cannot add placeholder for {label} (U+{codepoint:04X}) – missing in base font cmap")
        return

    # Use .notdef glyph as base for placeholder
    notdef = dst_font['glyf'][".notdef"].deepCopy()
    dst_font['glyf'][glyph_name] = notdef
    logging.info(f"Added placeholder for {label} (U+{codepoint:04X})")

def run_algorithm(base_font_path, donor_font_path, output_path):
    """Main processing pipeline: replace mapped glyphs + add placeholders."""
    logging.info("Loading fonts...")
    base = TTFont(base_font_path)
    donor = TTFont(donor_font_path)

    # Copy mapped glyphs
    for mal_cp, lat_cp in MAP_TABLE.items():
        copy_glyph(donor, base, lat_cp, mal_cp)

    # Add placeholders
    for cp, label in PLACEHOLDER_GLYPHS.items():
        add_placeholder(base, cp, label)

    # Ensure 'maxp' numGlyphs updated
    base['maxp'].numGlyphs = len(base['glyf'].glyphs)

    # Save new font
    base.save(output_path)
    logging.info(f"Saved modified font: {output_path}")
    return f"Font generated: {output_path}"
```

---

## ✅ How This Works

* You pick **any Malayalam font** in the 1st Browse.
* You pick **any Latin font** in the 2nd Browse.
* The algorithm will:

  1. Replace റ, ഠ, ധ, ന, ട with `n, o, w, m, s`.
  2. Add placeholders for ര, വ, സ (currently cloned from `.notdef` glyph).
* If fonts don’t contain a glyph, it logs `[WARN]` instead of breaking.

---

## 📦 How to Incorporate Into the App

Your folder structure now:

```
project/
 ├─ src/
 │   ├─ algorithm.py     # (patched version above)
 │   ├─ qa.py
 │   └─ ...
 ├─ font_transfer_app.py # GUI code
 ├─ setup.py
 ├─ config.json          # auto-created after first run
```

### Step 1: Put the patched `algorithm.py` into `src/`

### Step 2: Make sure `font_transfer_app.py` imports it correctly:


---
to be updated: 'font_transfer_app.py'
---
import tkinter as tk
from tkinter import filedialog, messagebox
import threading
import json
import os

from algorithm import run_algorithm  # your patched algorithm.py

CONFIG_FILE = "config.json"

def load_config():
    if os.path.exists(CONFIG_FILE):
        with open(CONFIG_FILE, "r") as f:
            return json.load(f)
    return {}

def save_config(noto_path, xyz_path):
    data = {"noto_font": noto_path, "xyz_font": xyz_path}
    with open(CONFIG_FILE, "w") as f:
        json.dump(data, f)

def start_process():
    noto_path = noto_entry.get()
    xyz_path = xyz_entry.get()
    if not (noto_path and xyz_path):
        messagebox.showerror("Error", "Select both fonts!")
        return
    output_path = filedialog.asksaveasfilename(
        defaultextension=".ttf", filetypes=[("TTF Font", "*.ttf")]
    )
    if not output_path:
        return

    status_label.config(text="Running...")

    def background_task():
        try:
            result = run_algorithm(noto_path, xyz_path, output_path)
            save_config(noto_path, xyz_path)  # Save last used fonts
            root.after(0, lambda: status_label.config(text=result))
            root.after(0, lambda: messagebox.showinfo("Success", result))
        except Exception as e:
            root.after(0, lambda: messagebox.showerror("Error", str(e)))

    threading.Thread(target=background_task).start()

# GUI Setup
root = tk.Tk()
root.title("Font Style Transfer App")

config = load_config()

tk.Label(root, text="Malayalam Base Font:").pack()
noto_entry = tk.Entry(root, width=50)
noto_entry.pack()
if "noto_font" in config:
    noto_entry.insert(0, config["noto_font"])
tk.Button(
    root,
    text="Browse",
    command=lambda: noto_entry.delete(0, tk.END) or noto_entry.insert(
        0, filedialog.askopenfilename(filetypes=[("TTF", "*.ttf")])
    ),
).pack()

tk.Label(root, text="Donor Latin Font:").pack()
xyz_entry = tk.Entry(root, width=50)
xyz_entry.pack()
if "xyz_font" in config:
    xyz_entry.insert(0, config["xyz_font"])
tk.Button(
    root,
    text="Browse",
    command=lambda: xyz_entry.delete(0, tk.END) or xyz_entry.insert(
        0, filedialog.askopenfilename(filetypes=[("TTF", "*.ttf")])
    ),
).pack()

tk.Button(root, text="Run", command=start_process).pack()
status_label = tk.Label(root, text="")
status_label.pack()

root.mainloop()
---

```python
from src.algorithm import run_algorithm
```

(if your file is at root, then `from algorithm import run_algorithm` is fine).

### Step 3: Rebuild the app with py2app:

```bash
python setup.py py2app
```

### Step 4: Run the `.app`

* Select Malayalam font in first window
* Select Latin font in second window
* Hit **Run** → It outputs a modified `.ttf`

---

