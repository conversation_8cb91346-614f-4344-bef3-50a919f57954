## File 1: `01_setup_and_env.md`

````markdown
# Step 1 — Setup Python Environment for Font Style Transfer App (macOS)

## 1. Install Python (via Homebrew)
```bash
brew install python@3.12
````

Verify:

```bash
python3.12 --version
```

---

## 2. Create Virtual Environment

```bash
python3.12 -m venv venv
source venv/bin/activate
```

(Every time you work, run `source venv/bin/activate`)

---

## 3. Install Dependencies

```bash
pip install --upgrade pip wheel setuptools
pip install fonttools ufoLib2 defcon skia-pathops fontmake harfbuzz numpy scipy py2app
```

Optional (for better GUI):

```bash
pip install pyside6
```

---

## 4. Project Folder Structure

```
font-style-transfer-app/
│── venv/                  # virtualenv
│── src/                   # Python scripts
│    ├── font_transfer_app.py
│    ├── algorithm.py       # modular functions (style extract, mapping, etc.)
│── setup.py               # for py2app
│── fonts/                 # sample input fonts
│── dist/                  # output .app after build
```

````

---