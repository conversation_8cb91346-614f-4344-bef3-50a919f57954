# Augment task: fix py2app `.app` launch failure and repro the `.app`

> Run all commands from the project root (where `setup.py` and `src/` live). Use a login shell (`bash`/`zsh`) so Homebrew and PATH changes take effect. If a command prints an error, capture and return the exact text.

---

### 0) Stop any running app & back up venv

```bash
# from project root
pwd
mv venv venv.bak || true
```

---

### 1) Install an official macOS Python build (python.org) via Homebrew cask

This gives a Python framework build that includes Tcl/Tk used by Tkinter.

```bash
brew update
brew install --cask python
# Refresh shell hash
hash -r
```

Check the Python you will use:

```bash
which python3
python3 --version
```

If `python3` still points to Homebrew non-framework Python, use explicit path likely created by cask:

```bash
# Typical python.org path; if this doesn't exist, print `which -a python3` and use the path shown
PYTHON_BIN="/Library/Frameworks/Python.framework/Versions/3.12/bin/python3"
if [ ! -x "$PYTHON_BIN" ]; then
  PYTHON_BIN="$(which python3)"
fi
echo "Using python at: $PYTHON_BIN"
$PYTHON_BIN --version
```

---

### 2) Create a fresh venv using that Python and activate it

```bash
# remove any leftover build artifacts
rm -rf build dist

# create new venv
"$PYTHON_BIN" -m venv venv
source venv/bin/activate

# confirm tkinter support inside venv
python - <<'PY'
try:
    import tkinter
    print("TKINTER_OK", tkinter.TkVersion)
except Exception as e:
    print("TKINTER_FAIL", repr(e))
PY
# if TKINTER_FAIL appears, stop and return the error; do not continue.
```

---

### 3) Upgrade pip and install all required dependencies

(we install both uharfbuzz and vharfbuzz; `uharfbuzz` is used by your QA, `vharfbuzz` is a friendly wrapper)

```bash
pip install --upgrade pip setuptools wheel
pip install fonttools ufoLib2 defcon skia-pathops fontmake uharfbuzz vharfbuzz numpy scipy py2app pyside6
# (optional) pyinstaller for fallback
pip install pyinstaller
```

Verify `uharfbuzz` loads:

```bash
python - <<'PY'
try:
    import uharfbuzz as hb
    print("UHARFBUZZ_OK", hb.__version__)
except Exception as e:
    print("UHARFBUZZ_FAIL", repr(e))
PY
```

---

### 4) Patch `setup.py` to use the GUI entry script and include frameworks

Create/overwrite `setup.py` at project root with exactly the following contents:

```python
from setuptools import setup
import os

# Use the GUI entry (src/font_transfer_app.py)
APP = ['src/font_transfer_app.py']

OPTIONS = {
    'argv_emulation': True,
    # 'packages' should be the module names as they are imported in code
    'packages': [
        'fontTools',    # import name fontTools
        'uharfbuzz',
        'skia_pathops',
        'ufoLib2',
        'defcon',
        'fontmake',
        'numpy',
        'scipy'
    ],
    'includes': ['tkinter', 'logging', 'json', 'copy', 'os'],
    # force-include Tcl/Tk framework paths used by python.org builds
    'frameworks': [
        '/Library/Frameworks/Tk.framework',
        '/Library/Frameworks/Tcl.framework'
    ],
    # Some modules may be pulled by import hooks; be explicit about hidden imports if you see errors
    'packages': ['fontTools', 'uharfbuzz', 'skia_pathops', 'ufoLib2', 'defcon', 'fontmake']
}

setup(
    app=APP,
    options={'py2app': OPTIONS},
    setup_requires=['py2app'],
)
```

> Note: the `frameworks` entry points py2app to include the system Tcl/Tk frameworks; this is required when bundling a framework python + tkinter.

---

### 5) Ensure `font_transfer_app.py` writes config to user support folder (not inside bundle)

Patch `src/font_transfer_app.py` — ensure it uses `~/Library/Application Support/FontTransfer/config.json`. Replace any `config.json` writes with:

```python
import os, json
CONFIG_DIR = os.path.expanduser("~/Library/Application Support/FontTransfer")
os.makedirs(CONFIG_DIR, exist_ok=True)
CONFIG_PATH = os.path.join(CONFIG_DIR, "config.json")
# use CONFIG_PATH for reading/writing
```

(If Augment already patched this, skip; otherwise apply the above changes.)

---

### 6) Clean build artifacts and run py2app build inside venv

```bash
# from project root
rm -rf build dist
python setup.py py2app -A   # -A builds an alias app first (faster). Remove -A for a full build.
# For final packaging run without -A:
python setup.py py2app
```

If build fails, capture and return full py2app error text. If it succeeds, continue.

---

### 7) Run the app binary from Terminal to see runtime errors (if any)

Run the executable directly to capture console output (this reveals missing imports):

```bash
APP_BIN="./dist/font_transfer_app.app/Contents/MacOS/font_transfer_app"
if [ -x "$APP_BIN" ]; then
  echo "Running app binary to capture logs..."
  "$APP_BIN" 2>&1 | tee ./dist/app_launch.log
  echo "app logs saved to dist/app_launch.log"
else
  echo "ERROR: app binary not found or not executable: $APP_BIN"
  ls -la dist || true
fi
```

* If the app runs and GUI opens, finish here.
* If you see `ModuleNotFoundError: No module named 'XXX'`, add that `'XXX'` to the `setup.py` `'includes'` or `'packages'` lists and rebuild (repeat step 4 → step 6). Return the exact missing module text if you want help adding it.

---

### 8) If py2app still fails (fallback): build with PyInstaller

Sometimes py2app struggles across macOS setups. Use PyInstaller as the fallback packager:

```bash
# Build a one-folder app (easier first)
pyinstaller --name FontTransfer --windowed --add-data "src:src" src/font_transfer_app.py

# Or a one-file build (may be larger)
pyinstaller --onefile --windowed --name FontTransfer src/font_transfer_app.py
```

After PyInstaller runs:

```bash
# One-folder build location:
ls -la dist/FontTransfer
# For onefile:
ls -la dist/FontTransfer
# Run the binary to capture logs (same as step 7)
./dist/FontTransfer
```

If PyInstaller succeeds, it provides an app-like bundle you can run (no py2app needed).

---

### 9) Final validation: run a quick algorithm test (command-line)

Ensure `run_algorithm` still works from the venv:

```bash
# Use sample fonts in your fonts/ folder
python -c "
from src.algorithm import run_algorithm
print(run_algorithm('fonts/NotoSansMalayalam-Regular.ttf','/System/Library/Fonts/Monaco.ttf','test_output.ttf'))
"
ls -l test_output.ttf || true
```

Then validate shaping via `src/qa.py`:

```bash
python src/qa.py test_output.ttf || python src/qa.py dist/test_output.ttf || true
```

---

### 10) If anything fails — gather diagnostics and return:

If any command fails, capture and return these files/outputs to me:

* `dist/app_launch.log` (if created)
* The `python setup.py py2app` error output
* The last `python - <<'PY'` outputs (tkinter/uharfbuzz checks)
* The `which python3` and `python3 --version` outputs
* The `ls -la dist` listing

---

## Notes & rationale

* The core problem was `_tkinter` missing (Homebrew python built without Tk support). Installing the **official framework Python** (python.org via brew cask) ensures a framework Python with Tcl/Tk, which makes Tkinter available and easier to bundle with py2app.
* `py2app` needs explicit guidance to include binary frameworks (Tcl/Tk) — the `frameworks` option helps.
* Recreating the venv with the correct Python ensures you have a consistent environment for `py2app` to inspect and package.
* Running the app binary from Terminal (`Contents/MacOS/...`) is the fastest way to see exactly what is missing at runtime; this is why step 7 is crucial.

---