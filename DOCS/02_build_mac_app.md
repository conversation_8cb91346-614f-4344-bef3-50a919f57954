## File 2: `02_build_mac_app.md`

```markdown
# Step 2 — Build Mac App for Font Style Transfer

## 1. Create Main App Script
File: `src/font_transfer_app.py`

```python
import tkinter as tk
from tkinter import filedialog, messagebox
import threading
from fonttools.ttLib import TTFont

# Import your real algorithm (to be written in algorithm.py)
from algorithm import run_algorithm

def start_process():
    noto_path = noto_entry.get()
    xyz_path = xyz_entry.get()
    if not (noto_path and xyz_path):
        messagebox.showerror("Error", "Select both fonts!")
        return
    output_path = filedialog.asksaveasfilename(defaultextension=".ttf", filetypes=[("TTF Font", "*.ttf")])
    if not output_path: return

    status_label.config(text="Running...")

    def background_task():
        try:
            result = run_algorithm(noto_path, xyz_path, output_path)
            root.after(0, lambda: status_label.config(text=result))
            root.after(0, lambda: messagebox.showinfo("Success", result))
        except Exception as e:
            root.after(0, lambda: messagebox.showerror("Error", str(e)))

    threading.Thread(target=background_task).start()

root = tk.Tk()
root.title("Font Style Transfer App")

tk.Label(root, text="Noto Sans Malayalam:").pack()
noto_entry = tk.Entry(root, width=50)
noto_entry.pack()
tk.Button(root, text="Browse", command=lambda: noto_entry.insert(0, filedialog.askopenfilename(filetypes=[("TTF", "*.ttf")]))).pack()

tk.Label(root, text="XYZ Font:").pack()
xyz_entry = tk.Entry(root, width=50)
xyz_entry.pack()
tk.Button(root, text="Browse", command=lambda: xyz_entry.insert(0, filedialog.askopenfilename(filetypes=[("TTF", "*.ttf")]))).pack()

tk.Button(root, text="Run", command=start_process).pack()
status_label = tk.Label(root, text="")
status_label.pack()

root.mainloop()
````

---

## 2. Add Algorithm Logic

File: `src/algorithm.py`

```python
from fonttools.ttLib import TTFont

def run_algorithm(noto_path, xyz_path, output_path):
    # Load both fonts
    noto = TTFont(noto_path)
    xyz = TTFont(xyz_path)

    # TODO: implement glyph style transfer here
    # Placeholder: copy noto and save
    noto.save(output_path)

    return f"Font generated: {output_path}"
```

---

## 3. Add `setup.py` for py2app

```python
from setuptools import setup

APP = ['src/font_transfer_app.py']
OPTIONS = {
    'argv_emulation': True,
    'packages': [
        'fonttools', 'ufoLib2', 'defcon',
        'skia_pathops', 'fontmake', 'harfbuzz',
        'numpy', 'scipy'
    ],
    'includes': ['tkinter', 'threading']
}

setup(
    app=APP,
    options={'py2app': OPTIONS},
    setup_requires=['py2app'],
)
```

---

## 4. Build the App

Activate venv:

```bash
source venv/bin/activate
```

Run py2app:

```bash
python setup.py py2app
```

Result: `.app` bundle in `dist/`

---

## 5. Run App

Double-click `dist/font_transfer_app.app`

If macOS blocks it (Gatekeeper), right-click → **Open**.

---